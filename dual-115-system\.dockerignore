# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3
data/

# Cookies
cookies/
*.txt

# Docker
.dockerignore
Dockerfile
docker-compose.yml

# Documentation
*.md
docs/

# Test files
test_*
*_test.py

# Temporary files
*.tmp
*.temp
.cache/
