# 双115系统部署指南

## 快速开始

### 1. 环境准备

- Python 3.8+
- 两个115账号（主115用于存储，副115用于播放）
- Emby服务器（已配置STRM文件）

### 2. 获取115 Cookies

#### 方法一：浏览器导出
1. 登录115网页版
2. 按F12打开开发者工具
3. 在Console中执行：`document.cookie`
4. 复制输出内容到文件

#### 方法二：使用p115client工具
```bash
pip install p115client
python -c "from p115client import P115Client; P115Client.login_scan_cookie('cookies.txt')"
```

### 3. 配置系统

复制环境变量配置文件：
```bash
cp .env.example .env
```

编辑`.env`文件，设置以下关键参数：
```bash
# 115账号配置
MAIN_115_COOKIES=/path/to/main-115-cookies.txt
SUB_115_COOKIES=/path/to/sub-115-cookies.txt

# 扫描目录配置
MAIN_SCAN_DIRS=0,123456789  # 主115目录ID
SUB_CACHE_DIR=987654321     # 副115缓存目录ID

# Emby服务器
EMBY_SERVER=http://localhost:8096
```

### 4. 获取115目录ID

1. 登录115网页版
2. 进入要扫描的目录
3. 查看浏览器地址栏URL
4. 提取`cid=`后面的数字，如：`https://115.com/?cid=123456789`

### 5. 部署方式

#### 方式一：Docker部署（推荐）

```bash
# 创建目录
mkdir -p cookies data logs

# 复制cookies文件
cp main-115-cookies.txt cookies/
cp sub-115-cookies.txt cookies/

# 修改docker-compose.yml配置
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

#### 方式二：直接运行

```bash
# 安装依赖
pip install -r requirements.txt

# 测试配置
python test_system.py

# 启动服务
python main.py
```

#### 方式三：使用启动脚本

Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

Windows:
```cmd
start.bat
```

### 6. 验证部署

1. **检查服务状态**
   ```bash
   curl http://localhost:8097/health
   ```

2. **查看统计信息**
   ```bash
   curl http://localhost:8097/api/stats
   ```

3. **手动触发扫描**
   ```bash
   curl http://localhost:8097/api/scan
   ```

### 7. Nginx配置

如果使用独立的Nginx，添加以下配置：

```nginx
upstream dual_115_backend {
    server localhost:8097;
}

upstream emby_backend {
    server localhost:8096;
}

server {
    listen 80;
    
    # 播放请求拦截
    location ~* ^/emby/Videos/\d+/(stream|master\.m3u8|hls) {
        proxy_pass http://dual_115_backend;
        proxy_set_header Host $host;
        proxy_buffering off;
    }
    
    # 其他请求代理到Emby
    location /emby/ {
        proxy_pass http://emby_backend;
        proxy_set_header Host $host;
    }
    
    # API接口
    location /api/ {
        proxy_pass http://dual_115_backend;
        proxy_set_header Host $host;
    }
}
```

## 常见问题

### Q: cookies过期怎么办？
A: 重新获取cookies文件，替换原文件后重启服务。

### Q: 文件复制失败？
A: 检查副115存储空间，确认缓存目录ID正确。

### Q: 播放没有走115？
A: 检查STRM文件路径格式，确认nginx配置正确。

### Q: 数据库文件过大？
A: 定期清理旧数据，或使用外部数据库。

### Q: 扫描速度慢？
A: 调整扫描间隔，减少扫描目录数量。

## 监控和维护

### 日志查看
```bash
# Docker
docker-compose logs -f dual-115-system

# 直接运行
tail -f logs/dual_115_system.log
```

### 数据库维护
```bash
# 查看数据库大小
ls -lh data/dual_115_system.db

# 清理旧数据（可选）
sqlite3 data/dual_115_system.db "DELETE FROM media_files WHERE updated_time < datetime('now', '-30 days');"
```

### 性能优化
1. 调整扫描间隔（UPDATE_INTERVAL）
2. 限制扫描目录范围
3. 使用SSD存储数据库
4. 增加副115存储空间

## 安全建议

1. 定期更新cookies文件
2. 限制API接口访问
3. 使用HTTPS（生产环境）
4. 备份数据库文件
5. 监控系统资源使用
