# 双115系统 Docker 部署指南

## 📦 项目结构

```
dual-115-system/
├── dual_115_system.py    # 核心系统类
├── nginx_proxy.py        # Flask代理应用
├── config.py            # 配置管理
├── main.py              # 主程序入口
├── requirements.txt     # Python依赖
├── Dockerfile          # Docker镜像构建
├── docker-compose.yml  # Docker编排
├── nginx.conf          # Nginx配置
├── .env.example        # 环境变量示例
├── .dockerignore       # Docker忽略文件
├── deploy.sh           # Linux/Mac部署脚本
├── deploy.bat          # Windows部署脚本
├── README.md           # 项目说明
└── DOCKER_GUIDE.md     # Docker部署指南
```

## 🚀 快速部署

### 方法一：使用部署脚本（推荐）

**Linux/Mac:**
```bash
chmod +x deploy.sh
./deploy.sh
```

**Windows:**
```cmd
deploy.bat
```

### 方法二：手动部署

1. **准备cookies文件**
   ```bash
   mkdir -p cookies data logs
   # 将115 cookies文件复制到cookies目录
   cp /path/to/main-115-cookies.txt cookies/
   cp /path/to/sub-115-cookies.txt cookies/
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑.env文件，设置必要参数
   nano .env
   ```

3. **启动服务**
   ```bash
   docker-compose up -d
   ```

## ⚙️ 配置说明

### 必需配置

在 `.env` 文件中设置以下参数：

```bash
# 115账号配置
MAIN_115_COOKIES=/app/cookies/main-115-cookies.txt
SUB_115_COOKIES=/app/cookies/sub-115-cookies.txt

# 扫描配置
MAIN_SCAN_DIRS=0,123456789  # 主115扫描目录ID
SUB_CACHE_DIR=987654321     # 副115缓存目录ID

# Emby服务器
EMBY_SERVER=http://emby:8096
```

### 可选配置

```bash
# 更新间隔（秒）
UPDATE_INTERVAL=3600

# 文件查找策略
FILE_SEARCH_STRATEGY=hybrid

# 日志级别
LOG_LEVEL=INFO
```

## 🔧 Docker Compose 配置

### 基础配置

```yaml
version: '3.8'

services:
  dual-115-system:
    build: .
    container_name: dual-115-system
    restart: unless-stopped
    ports:
      - "8097:8097"
    volumes:
      - ./cookies:/app/cookies:ro
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - MAIN_115_COOKIES=/app/cookies/main-115-cookies.txt
      - SUB_115_COOKIES=/app/cookies/sub-115-cookies.txt
      # ... 其他环境变量
```

### 高级配置

```yaml
services:
  dual-115-system:
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8097/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🌐 网络配置

### 独立部署

如果Emby运行在其他服务器上：

```yaml
services:
  dual-115-system:
    environment:
      - EMBY_SERVER=http://*************:8096
    ports:
      - "8097:8097"
```

### 与现有Emby集成

如果Emby已经在Docker中运行：

```yaml
services:
  dual-115-system:
    environment:
      - EMBY_SERVER=http://emby:8096
    networks:
      - emby-network

networks:
  emby-network:
    external: true
```

## 📊 监控和维护

### 查看服务状态

```bash
# 查看容器状态
docker-compose ps

# 查看实时日志
docker-compose logs -f dual-115-system

# 查看资源使用
docker stats dual-115-system
```

### 健康检查

```bash
# 应用健康检查
curl http://localhost:8097/health

# 获取统计信息
curl http://localhost:8097/api/stats

# 手动触发扫描
curl http://localhost:8097/api/scan
```

### 数据备份

```bash
# 备份数据库
cp data/dual_115_system.db data/dual_115_system.db.backup

# 备份配置
tar -czf backup-$(date +%Y%m%d).tar.gz .env cookies/ data/
```

## 🔄 更新和维护

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建镜像
docker-compose build --no-cache

# 重启服务
docker-compose up -d
```

### 清理资源

```bash
# 清理未使用的镜像
docker image prune

# 清理未使用的容器
docker container prune

# 清理未使用的卷
docker volume prune
```

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs dual-115-system
   
   # 检查配置文件
   docker-compose config
   ```

2. **cookies过期**
   ```bash
   # 更新cookies文件
   cp new-cookies.txt cookies/main-115-cookies.txt
   
   # 重启容器
   docker-compose restart dual-115-system
   ```

3. **端口冲突**
   ```bash
   # 修改端口映射
   # 在docker-compose.yml中修改ports配置
   ports:
     - "8098:8097"  # 使用8098端口
   ```

4. **存储空间不足**
   ```bash
   # 检查磁盘空间
   df -h
   
   # 清理Docker资源
   docker system prune -a
   ```

### 调试模式

启用调试模式获取更多日志信息：

```yaml
environment:
  - LOG_LEVEL=DEBUG
  - FLASK_DEBUG=true
```

### 性能调优

```yaml
environment:
  # 数据库缓存策略
  - FILE_SEARCH_STRATEGY=database  # 高性能
  - DATABASE_CACHE_TTL=86400
  
  # 扫描间隔优化
  - UPDATE_INTERVAL=7200  # 2小时扫描一次
```

## 🔒 安全配置

### 网络安全

```yaml
services:
  dual-115-system:
    # 只绑定本地接口
    ports:
      - "127.0.0.1:8097:8097"
    
    # 使用内部网络
    networks:
      - internal
    
networks:
  internal:
    internal: true
```

### 文件权限

```bash
# 设置cookies文件权限
chmod 600 cookies/*.txt

# 设置数据目录权限
chmod 755 data/
chmod 644 data/*.db
```

## 📈 扩展配置

### 多实例部署

```yaml
services:
  dual-115-system-1:
    build: .
    container_name: dual-115-system-1
    ports:
      - "8097:8097"
    environment:
      - MAIN_SCAN_DIRS=123456789
  
  dual-115-system-2:
    build: .
    container_name: dual-115-system-2
    ports:
      - "8098:8097"
    environment:
      - MAIN_SCAN_DIRS=987654321
```

### 负载均衡

```yaml
services:
  nginx-lb:
    image: nginx:alpine
    ports:
      - "8096:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - dual-115-system-1
      - dual-115-system-2
```

这个Docker部署指南提供了从基础部署到高级配置的完整说明，帮助用户根据不同需求进行部署和维护。
