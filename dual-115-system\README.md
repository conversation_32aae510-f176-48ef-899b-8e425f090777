# 双115系统 - Docker版本

基于p115client实现的双115系统，用于优化Emby媒体服务器的播放体验。

## 🚀 快速开始

### 1. 准备工作

1. **获取115 Cookies文件**
   ```bash
   # 方法1：使用p115client工具
   pip install p115client
   python -c "from p115client import P115Client; P115Client.login_scan_cookie('main-115-cookies.txt')"
   python -c "from p115client import P115Client; P115Client.login_scan_cookie('sub-115-cookies.txt')"
   
   # 方法2：浏览器导出（登录115后按F12，在Console执行）
   # document.cookie
   ```

2. **获取115目录ID**
   - 登录115网页版
   - 进入要扫描的目录
   - 查看URL中的cid参数：`https://115.com/?cid=123456789`

### 2. Docker部署

```bash
# 克隆或下载项目文件
git clone <repository-url>
cd dual-115-system

# 创建必要的目录
mkdir -p cookies data logs

# 复制115 cookies文件
cp /path/to/main-115-cookies.txt cookies/
cp /path/to/sub-115-cookies.txt cookies/

# 修改docker-compose.yml配置
# - 设置正确的媒体库路径
# - 配置115目录ID
# - 调整其他参数

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f dual-115-system
```

### 3. 配置说明

编辑 `docker-compose.yml` 中的环境变量：

```yaml
environment:
  # 115账号配置
  - MAIN_115_COOKIES=/app/cookies/main-115-cookies.txt
  - SUB_115_COOKIES=/app/cookies/sub-115-cookies.txt
  
  # 扫描配置
  - MAIN_SCAN_DIRS=0,123456789  # 主115扫描目录ID
  - SUB_CACHE_DIR=987654321     # 副115缓存目录ID
  - UPDATE_INTERVAL=3600        # 更新间隔（秒）
  
  # Emby服务器
  - EMBY_SERVER=http://emby:8096
  
  # 文件查找策略（推荐hybrid）
  - FILE_SEARCH_STRATEGY=hybrid
```

## 📊 功能特性

1. **双115架构**：主115存储，副115播放
2. **智能代理**：nginx反代emby，劫持播放请求
3. **混合查找**：数据库缓存 + 实时搜索兜底
4. **字幕支持**：自动识别和复制字幕文件
5. **API接口**：完整的管理和监控API

## 🔧 API接口

```bash
# 健康检查
curl http://localhost:8097/health

# 手动扫描
curl http://localhost:8097/api/scan

# 统计信息
curl http://localhost:8097/api/stats

# 字幕列表
curl http://localhost:8097/api/subtitles/{media_file_id}
```

## 📁 目录结构

```
dual-115-system/
├── dual_115_system.py    # 核心系统类
├── nginx_proxy.py        # Flask代理应用
├── config.py            # 配置管理
├── main.py              # 主程序入口
├── requirements.txt     # Python依赖
├── Dockerfile          # Docker镜像构建
├── docker-compose.yml  # Docker编排
├── nginx.conf          # Nginx配置
├── .env.example        # 环境变量示例
├── .dockerignore       # Docker忽略文件
└── README.md           # 说明文档
```

## 🔄 工作流程

1. **初始化**：连接双115，初始化数据库
2. **定期扫描**：扫描主115目录，识别媒体和字幕文件
3. **播放拦截**：nginx拦截Emby播放请求
4. **智能查找**：优先数据库，失败时实时搜索
5. **文件复制**：按需复制媒体文件和字幕到副115
6. **302重定向**：返回副115播放链接

## 🎬 字幕支持

### 支持格式
- `.srt` - SubRip字幕（推荐）
- `.ass` - Advanced SubStation Alpha
- `.ssa` - SubStation Alpha
- `.vtt` - WebVTT字幕

### 命名规范
```
电影名称.语言.类型.扩展名
```

示例：
- `电影名称.srt` - 默认字幕
- `电影名称.zh.srt` - 中文字幕
- `电影名称.en.srt` - 英文字幕
- `电影名称.zh.forced.srt` - 中文强制字幕

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查日志
   docker-compose logs dual-115-system
   
   # 检查配置
   docker-compose config
   ```

2. **cookies过期**
   ```bash
   # 重新获取cookies文件
   # 替换cookies目录中的文件
   # 重启容器
   docker-compose restart dual-115-system
   ```

3. **文件复制失败**
   ```bash
   # 检查副115存储空间
   # 确认缓存目录ID正确
   # 查看详细日志
   ```

### 日志查看

```bash
# 实时日志
docker-compose logs -f dual-115-system

# 历史日志
docker-compose logs --tail=100 dual-115-system

# 进入容器
docker-compose exec dual-115-system bash
```

## ⚙️ 高级配置

### 性能调优

```yaml
# 高性能场景（纯数据库）
- FILE_SEARCH_STRATEGY=database
- ENABLE_REALTIME_FALLBACK=false

# 高准确性场景（实时搜索）
- FILE_SEARCH_STRATEGY=realtime

# 平衡场景（混合模式，推荐）
- FILE_SEARCH_STRATEGY=hybrid
- ENABLE_REALTIME_FALLBACK=true
```

### 资源限制

```yaml
services:
  dual-115-system:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

## 📈 监控

### Prometheus指标（可选）

```yaml
# 添加到docker-compose.yml
- ENABLE_METRICS=true
- METRICS_PORT=9090
```

### 健康检查

```bash
# Docker健康检查
docker-compose ps

# 应用健康检查
curl http://localhost:8097/health
```

## 🔒 安全建议

1. **网络安全**
   - 使用防火墙限制端口访问
   - 配置反向代理SSL证书
   - 定期更新Docker镜像

2. **数据安全**
   - 定期备份数据库文件
   - 保护cookies文件安全
   - 监控系统资源使用

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持双115架构
- 完整的字幕处理
- 混合查找策略
- Docker容器化部署

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目基于MIT许可证开源。
