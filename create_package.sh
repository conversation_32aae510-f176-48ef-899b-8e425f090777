#!/bin/bash

# 双115系统打包脚本
# 将所有必要文件打包成可分发的压缩包

set -e

echo "=== 双115系统打包脚本 ==="

# 定义变量
PACKAGE_NAME="dual-115-system"
VERSION="v1.0.0"
PACKAGE_DIR="${PACKAGE_NAME}-${VERSION}"
ARCHIVE_NAME="${PACKAGE_DIR}.tar.gz"

# 清理旧的打包文件
if [ -d "$PACKAGE_DIR" ]; then
    echo "清理旧的打包目录..."
    rm -rf "$PACKAGE_DIR"
fi

if [ -f "$ARCHIVE_NAME" ]; then
    echo "清理旧的压缩包..."
    rm -f "$ARCHIVE_NAME"
fi

# 创建打包目录
echo "创建打包目录: $PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR"

# 复制核心文件
echo "复制核心文件..."
cp dual-115-system/dual_115_system.py "$PACKAGE_DIR/"
cp dual-115-system/nginx_proxy.py "$PACKAGE_DIR/"
cp dual-115-system/config.py "$PACKAGE_DIR/"
cp dual-115-system/main.py "$PACKAGE_DIR/"

# 复制Docker相关文件
echo "复制Docker相关文件..."
cp dual-115-system/Dockerfile "$PACKAGE_DIR/"
cp dual-115-system/docker-compose.yml "$PACKAGE_DIR/"
cp dual-115-system/nginx.conf "$PACKAGE_DIR/"
cp dual-115-system/requirements.txt "$PACKAGE_DIR/"
cp dual-115-system/.dockerignore "$PACKAGE_DIR/"

# 复制配置文件
echo "复制配置文件..."
cp dual-115-system/.env.example "$PACKAGE_DIR/"

# 复制部署脚本
echo "复制部署脚本..."
cp dual-115-system/deploy.sh "$PACKAGE_DIR/"
cp dual-115-system/deploy.bat "$PACKAGE_DIR/"
chmod +x "$PACKAGE_DIR/deploy.sh"

# 复制文档
echo "复制文档文件..."
cp dual-115-system/README.md "$PACKAGE_DIR/"
cp dual-115-system/DOCKER_GUIDE.md "$PACKAGE_DIR/"

# 复制其他文档（如果存在）
if [ -f "ARCHITECTURE_COMPARISON.md" ]; then
    cp ARCHITECTURE_COMPARISON.md "$PACKAGE_DIR/"
fi

if [ -f "SUBTITLE_HANDLING.md" ]; then
    cp SUBTITLE_HANDLING.md "$PACKAGE_DIR/"
fi

if [ -f "DEPLOY.md" ]; then
    cp DEPLOY.md "$PACKAGE_DIR/"
fi

# 创建示例目录结构
echo "创建示例目录结构..."
mkdir -p "$PACKAGE_DIR/cookies"
mkdir -p "$PACKAGE_DIR/data"
mkdir -p "$PACKAGE_DIR/logs"

# 创建cookies示例文件
cat > "$PACKAGE_DIR/cookies/README.md" << 'EOF'
# Cookies 文件说明

请将115账号的cookies文件放置在此目录下：

- `main-115-cookies.txt` - 主115账号cookies（用于存储）
- `sub-115-cookies.txt` - 副115账号cookies（用于播放）

## 获取cookies文件的方法

### 方法1：使用p115client工具
```bash
pip install p115client
python -c "from p115client import P115Client; P115Client.login_scan_cookie('main-115-cookies.txt')"
python -c "from p115client import P115Client; P115Client.login_scan_cookie('sub-115-cookies.txt')"
```

### 方法2：浏览器导出
1. 登录115网页版
2. 按F12打开开发者工具
3. 在Console中执行：`document.cookie`
4. 复制输出内容到文件

## 注意事项

- cookies文件包含敏感信息，请妥善保管
- cookies有有效期，过期后需要重新获取
- 建议定期更新cookies文件
EOF

# 创建快速开始指南
cat > "$PACKAGE_DIR/QUICK_START.md" << 'EOF'
# 快速开始指南

## 1. 准备工作

1. 安装Docker和Docker Compose
2. 获取115账号cookies文件
3. 获取115目录ID

## 2. 部署步骤

### Linux/Mac
```bash
# 1. 解压文件
tar -xzf dual-115-system-v1.0.0.tar.gz
cd dual-115-system-v1.0.0

# 2. 复制cookies文件
cp /path/to/main-115-cookies.txt cookies/
cp /path/to/sub-115-cookies.txt cookies/

# 3. 运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

### Windows
```cmd
# 1. 解压文件到目录
# 2. 复制cookies文件到cookies目录
# 3. 双击运行 deploy.bat
```

## 3. 验证部署

访问以下地址验证部署：
- 健康检查: http://localhost:8097/health
- 统计信息: http://localhost:8097/api/stats
- Emby代理: http://localhost:8096

## 4. 常用命令

```bash
# 查看日志
docker-compose logs -f dual-115-system

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 手动扫描
curl http://localhost:8097/api/scan
```

## 5. 配置说明

主要配置项在 `.env` 文件中：
- `MAIN_SCAN_DIRS` - 主115扫描目录ID
- `SUB_CACHE_DIR` - 副115缓存目录ID
- `EMBY_SERVER` - Emby服务器地址

详细配置说明请参考 `README.md` 和 `DOCKER_GUIDE.md`。
EOF

# 创建版本信息文件
cat > "$PACKAGE_DIR/VERSION" << EOF
双115系统 ${VERSION}

构建时间: $(date '+%Y-%m-%d %H:%M:%S')
构建平台: $(uname -s) $(uname -m)

主要功能:
- 双115架构（主115存储，副115播放）
- 智能文件查找（数据库缓存+实时搜索）
- 完整字幕支持（自动识别和复制）
- Nginx反向代理（劫持Emby播放请求）
- Docker容器化部署
- 完整的API接口

技术栈:
- Python 3.11
- Flask Web框架
- p115client库
- SQLite数据库
- Nginx反向代理
- Docker容器化

更新日志:
- v1.0.0: 初始版本发布
EOF

# 创建许可证文件
cat > "$PACKAGE_DIR/LICENSE" << 'EOF'
MIT License

Copyright (c) 2024 Dual 115 System

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
EOF

# 创建压缩包
echo "创建压缩包: $ARCHIVE_NAME"
tar -czf "$ARCHIVE_NAME" "$PACKAGE_DIR"

# 计算文件大小和校验和
FILE_SIZE=$(du -h "$ARCHIVE_NAME" | cut -f1)
FILE_HASH=$(sha256sum "$ARCHIVE_NAME" | cut -d' ' -f1)

# 显示打包结果
echo
echo "=== 打包完成 ==="
echo "压缩包: $ARCHIVE_NAME"
echo "文件大小: $FILE_SIZE"
echo "SHA256: $FILE_HASH"
echo
echo "包含文件:"
tar -tzf "$ARCHIVE_NAME" | head -20
if [ $(tar -tzf "$ARCHIVE_NAME" | wc -l) -gt 20 ]; then
    echo "... 还有 $(($(tar -tzf "$ARCHIVE_NAME" | wc -l) - 20)) 个文件"
fi

echo
echo "部署说明:"
echo "1. 解压: tar -xzf $ARCHIVE_NAME"
echo "2. 进入目录: cd $PACKAGE_DIR"
echo "3. 查看快速开始: cat QUICK_START.md"
echo "4. 运行部署脚本: ./deploy.sh (Linux/Mac) 或 deploy.bat (Windows)"

# 清理临时目录
echo
echo "清理临时文件..."
rm -rf "$PACKAGE_DIR"

echo "打包完成！"
EOF
