# 双115系统环境变量配置示例
# 复制此文件为 .env 并修改相应配置

# 115账号配置
# 主115 cookies文件路径（用于存储）
MAIN_115_COOKIES=~/main-115-cookies.txt

# 副115 cookies文件路径（用于播放）
SUB_115_COOKIES=~/sub-115-cookies.txt

# 数据库配置
# SQLite数据库文件路径
DB_PATH=data/dual_115_system.db

# 扫描配置
# 主115扫描目录ID列表，多个用逗号分隔
# 获取方法：登录115网页版，进入目录，查看URL中的cid参数
MAIN_SCAN_DIRS=0,123456789,987654321

# 副115缓存目录ID（用于存放复制的文件）
SUB_CACHE_DIR=111111111

# 扫描更新间隔（秒）
# 3600 = 1小时，7200 = 2小时，86400 = 24小时
UPDATE_INTERVAL=3600

# Emby服务器配置
# Emby服务器地址
EMBY_SERVER=http://localhost:8096

# Flask应用配置
# 监听地址
FLASK_HOST=0.0.0.0

# 监听端口
FLASK_PORT=8097

# 调试模式（生产环境请设为false）
FLASK_DEBUG=false

# 日志配置
# 日志级别：DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/dual_115_system.log

# 文件查找策略配置
# 可选值：database（数据库）, realtime（实时）, hybrid（混合，推荐）
FILE_SEARCH_STRATEGY=hybrid

# 是否启用实时搜索兜底（仅hybrid模式有效）
ENABLE_REALTIME_FALLBACK=true

# 是否验证副115文件存在性
VERIFY_SUB_FILE_EXISTS=true

# 数据库缓存TTL（秒，86400=24小时）
DATABASE_CACHE_TTL=86400

# 实时搜索超时时间（秒）
REALTIME_SEARCH_TIMEOUT=10

# Docker相关配置（仅在Docker部署时需要）
# 媒体库挂载路径
MEDIA_PATH=/path/to/emby/media

# 数据持久化路径
DATA_PATH=./data

# 日志持久化路径
LOGS_PATH=./logs

# Cookies文件路径
COOKIES_PATH=./cookies
