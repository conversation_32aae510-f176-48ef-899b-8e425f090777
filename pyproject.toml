[tool.poetry]
name = "p115client"
version = "********.6"
description = "Python 115 webdisk client."
authors = ["<PERSON><PERSON> <<EMAIL>>"]
license = "MIT"
readme = "readme.md"
homepage = "https://github.com/Chenyang<PERSON>ao/p115client"
repository = "https://github.com/ChenyangGao/p115client"
documentation = "https://p115client.readthedocs.io"
keywords = ["115", "webdisk", "client"]
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Development Status :: 4 - Beta",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3 :: Only",
    "Operating System :: OS Independent",
    "Intended Audience :: Developers",
    "Topic :: Software Development",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
include = [
    "LICENSE",
]

[tool.poetry.dependencies]
python = "^3.12"
ed2k = ">=*******"
http_response = ">=*******"
httpx = ">=0.28"
httpx_request = ">=0.1.4"
iter_collect = ">=*******"
multidict = "*"
orjson = "*"
p115cipher = ">=0.0.3"
p115pickcode = ">=0.0.5"
posixpatht = ">=0.0.3"
python-argtools = ">=0.0.1"
python-asynctools = ">=0.1.3"
python-concurrenttools = ">=0.1.0"
python-cookietools = ">=*******"
python-dictattr = ">=0.0.4"
python-encode_uri = ">=0.0.1"
python-filewrap = ">=0.2.8"
python-hashtools = ">=*******"
python-httpfile = ">=*******"
python-http_request = ">=0.0.6"
python-iterutils = ">=0.2.7"
python-property = ">=0.0.3"
python-startfile = ">=0.0.2"
python-undefined = ">=0.0.3"
qrcode = "*"
yarl = "*"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.packages]]
include = "p115client"
