#!/bin/bash

# 双115系统Docker部署脚本

set -e

echo "=== 双115系统Docker部署脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_docker() {
    print_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    
    mkdir -p cookies data logs
    
    print_success "目录创建完成"
}

# 检查cookies文件
check_cookies() {
    print_info "检查115 cookies文件..."
    
    if [ ! -f "cookies/main-115-cookies.txt" ]; then
        print_warning "主115 cookies文件不存在: cookies/main-115-cookies.txt"
        print_info "请将主115的cookies文件复制到 cookies/main-115-cookies.txt"
        read -p "是否继续部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    if [ ! -f "cookies/sub-115-cookies.txt" ]; then
        print_warning "副115 cookies文件不存在: cookies/sub-115-cookies.txt"
        print_info "请将副115的cookies文件复制到 cookies/sub-115-cookies.txt"
        read -p "是否继续部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_success "Cookies文件检查完成"
}

# 配置向导
configure_system() {
    print_info "开始配置向导..."
    
    # 检查是否存在.env文件
    if [ ! -f ".env" ]; then
        print_info "创建.env配置文件..."
        cp .env.example .env
        
        print_info "请编辑.env文件配置以下参数："
        print_info "1. MAIN_SCAN_DIRS - 主115扫描目录ID"
        print_info "2. SUB_CACHE_DIR - 副115缓存目录ID"
        print_info "3. EMBY_SERVER - Emby服务器地址"
        print_info "4. 其他可选参数"
        
        read -p "是否现在编辑配置文件？(Y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Nn]$ ]]; then
            ${EDITOR:-nano} .env
        fi
    else
        print_info "发现现有.env配置文件"
    fi
    
    print_success "配置完成"
}

# 构建和启动服务
deploy_services() {
    print_info "构建Docker镜像..."
    docker-compose build
    
    print_info "启动服务..."
    docker-compose up -d
    
    print_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    print_info "验证部署状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查容器状态
    if docker-compose ps | grep -q "Up"; then
        print_success "容器启动成功"
    else
        print_error "容器启动失败"
        docker-compose logs
        exit 1
    fi
    
    # 检查健康状态
    print_info "检查服务健康状态..."
    for i in {1..30}; do
        if curl -s http://localhost:8097/health > /dev/null; then
            print_success "服务健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            print_error "服务健康检查失败"
            docker-compose logs dual-115-system
            exit 1
        fi
        
        sleep 2
    done
}

# 显示部署信息
show_deployment_info() {
    print_success "=== 部署完成 ==="
    echo
    print_info "服务访问地址："
    print_info "  - 双115系统API: http://localhost:8097"
    print_info "  - Emby代理地址: http://localhost:8096"
    print_info "  - 健康检查: http://localhost:8097/health"
    echo
    print_info "常用命令："
    print_info "  - 查看日志: docker-compose logs -f dual-115-system"
    print_info "  - 重启服务: docker-compose restart"
    print_info "  - 停止服务: docker-compose down"
    print_info "  - 手动扫描: curl http://localhost:8097/api/scan"
    print_info "  - 查看统计: curl http://localhost:8097/api/stats"
    echo
    print_info "配置文件位置："
    print_info "  - 环境变量: .env"
    print_info "  - Docker编排: docker-compose.yml"
    print_info "  - Nginx配置: nginx.conf"
    echo
    print_warning "注意事项："
    print_warning "  - 请确保115 cookies文件有效"
    print_warning "  - 定期检查副115存储空间"
    print_warning "  - 监控系统日志和性能"
}

# 主函数
main() {
    echo
    print_info "开始部署双115系统..."
    echo
    
    check_docker
    create_directories
    check_cookies
    configure_system
    deploy_services
    verify_deployment
    show_deployment_info
    
    echo
    print_success "双115系统部署完成！"
}

# 错误处理
trap 'print_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
