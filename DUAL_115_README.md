# 双115系统 - 主115存储，副115播放

基于p115client实现的双115系统，用于优化Emby媒体服务器的播放体验。

## 功能特性

1. **双115架构**：主115用于存储影音文件，副115用于播放影音文件
2. **自动同步**：遍历主115指定目录并写入数据库，支持定期更新
3. **智能代理**：nginx反代emby，劫持播放请求
4. **按需复制**：根据播放需求自动复制文件到副115
5. **302重定向**：直接播放副115中的影音文件

## 系统架构

```
用户 -> Nginx -> Emby (STRM文件)
              -> 双115系统 -> 主115 (存储)
                          -> 副115 (播放)
```

## 安装部署

### 1. 准备工作

1. 准备两个115账号的cookies文件
2. 在副115中创建一个专门的缓存目录
3. 确保Emby已配置好STRM文件

### 2. Docker部署（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd dual-115-system

# 创建必要的目录
mkdir -p cookies data logs

# 复制115 cookies文件到cookies目录
cp /path/to/main-115-cookies.txt cookies/
cp /path/to/sub-115-cookies.txt cookies/

# 修改docker-compose.yml中的配置
# - 设置正确的媒体库路径
# - 配置115目录ID
# - 调整其他参数

# 启动服务
docker-compose up -d
```

### 3. 手动部署

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
export MAIN_115_COOKIES="~/main-115-cookies.txt"
export SUB_115_COOKIES="~/sub-115-cookies.txt"
export MAIN_SCAN_DIRS="0,123456789"  # 主115扫描目录ID
export SUB_CACHE_DIR="987654321"     # 副115缓存目录ID

# 启动服务
python main.py
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `MAIN_115_COOKIES` | 主115 cookies文件路径 | `~/main-115-cookies.txt` |
| `SUB_115_COOKIES` | 副115 cookies文件路径 | `~/sub-115-cookies.txt` |
| `MAIN_SCAN_DIRS` | 主115扫描目录ID（逗号分隔） | `0` |
| `SUB_CACHE_DIR` | 副115缓存目录ID | `0` |
| `UPDATE_INTERVAL` | 扫描更新间隔（秒） | `3600` |
| `EMBY_SERVER` | Emby服务器地址 | `http://localhost:8096` |
| `FLASK_PORT` | Flask应用端口 | `8097` |

### 获取115目录ID

1. 登录115网页版
2. 进入要扫描的目录
3. 查看URL中的cid参数，如：`https://115.com/?cid=123456789`
4. 其中`123456789`就是目录ID

## API接口

### 手动扫描
```bash
curl http://localhost:8097/api/scan
```

### 获取统计信息
```bash
curl http://localhost:8097/api/stats
```

### 健康检查
```bash
curl http://localhost:8097/health
```

## 工作流程

1. **初始化**：系统启动时连接主115和副115，初始化数据库
2. **定期扫描**：按设定间隔扫描主115指定目录，将文件信息存入数据库
3. **播放请求**：用户通过Emby播放STRM文件时，nginx拦截播放请求
4. **路径解析**：解析STRM文件内容，提取115文件路径
5. **数据库查询**：在数据库中查找对应的文件信息
6. **文件复制**：如果文件未复制到副115，则自动复制
7. **302重定向**：返回副115的播放链接，实现无缝播放

## 注意事项

1. **cookies有效性**：确保115 cookies文件有效且定期更新
2. **存储空间**：副115需要足够的存储空间用于缓存
3. **网络带宽**：文件复制需要消耗带宽，建议在低峰期进行
4. **STRM格式**：确保STRM文件路径格式正确
5. **权限设置**：确保程序有读写数据库和日志文件的权限

## 故障排除

### 常见问题

1. **cookies过期**：重新获取115 cookies文件
2. **目录ID错误**：检查115目录ID是否正确
3. **网络连接**：检查115服务器连接状态
4. **权限问题**：检查文件和目录权限
5. **端口冲突**：修改Flask端口配置

### 日志查看

```bash
# Docker部署
docker-compose logs -f dual-115-system

# 手动部署
tail -f dual_115_system.log
```

## 开发说明

### 项目结构

```
dual-115-system/
├── dual_115_system.py    # 核心系统类
├── nginx_proxy.py        # Flask代理应用
├── config.py            # 配置管理
├── main.py              # 主程序入口
├── requirements.txt     # Python依赖
├── Dockerfile          # Docker镜像构建
├── docker-compose.yml  # Docker编排
├── nginx.conf          # Nginx配置
└── DUAL_115_README.md  # 说明文档
```

## 许可证

本项目基于MIT许可证开源。
