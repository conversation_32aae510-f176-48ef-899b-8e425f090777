#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双115系统主程序
同时启动定期扫描任务和Flask Web服务
"""

import threading
import time
import logging
from pathlib import Path

from config import get_config, validate_config
from dual_115_system import Dual115System
from nginx_proxy import app

# 配置日志
def setup_logging(config):
    """设置日志配置"""
    log_level = getattr(logging, config['log_level'].upper(), logging.INFO)
    
    # 确保日志目录存在
    log_file = Path(config['log_file'])
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def run_periodic_scan(dual_system):
    """运行定期扫描任务"""
    logger = logging.getLogger('periodic_scan')
    logger.info("启动定期扫描线程")
    
    try:
        dual_system.periodic_scan()
    except Exception as e:
        logger.error(f"定期扫描线程异常: {e}")

def run_flask_app(config):
    """运行Flask应用"""
    logger = logging.getLogger('flask_app')
    logger.info(f"启动Flask应用: {config['flask_host']}:{config['flask_port']}")
    
    try:
        app.run(
            host=config['flask_host'],
            port=config['flask_port'],
            debug=config['flask_debug'],
            threaded=True
        )
    except Exception as e:
        logger.error(f"Flask应用异常: {e}")

def main():
    """主函数"""
    # 加载配置
    config = get_config()
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger('main')
    
    # 验证配置
    errors = validate_config()
    if errors:
        logger.error("配置验证失败:")
        for error in errors:
            logger.error(f"  - {error}")
        return 1
    
    logger.info("配置验证通过")
    
    try:
        # 初始化双115系统
        dual_system = Dual115System(config)
        
        # 启动定期扫描线程
        scan_thread = threading.Thread(
            target=run_periodic_scan,
            args=(dual_system,),
            daemon=True
        )
        scan_thread.start()
        
        # 等待一下让扫描线程启动
        time.sleep(2)
        
        # 启动Flask应用（主线程）
        logger.info("双115系统启动完成")
        run_flask_app(config)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
