#!/bin/bash

# 双115系统启动脚本

set -e

echo "=== 双115系统启动脚本 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+' | head -1)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "错误: 需要Python 3.8或更高版本，当前版本: $python_version"
    exit 1
fi

echo "Python版本检查通过: $python_version"

# 检查必要的文件
required_files=("dual_115_system.py" "nginx_proxy.py" "config.py" "main.py" "requirements.txt")

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 缺少必要文件: $file"
        exit 1
    fi
done

echo "文件检查通过"

# 安装依赖
echo "安装Python依赖..."
pip3 install -r requirements.txt

# 检查环境变量
echo "检查配置..."

if [ -z "$MAIN_115_COOKIES" ]; then
    echo "警告: 未设置MAIN_115_COOKIES环境变量，使用默认值"
fi

if [ -z "$SUB_115_COOKIES" ]; then
    echo "警告: 未设置SUB_115_COOKIES环境变量，使用默认值"
fi

# 验证配置
echo "验证配置..."
python3 config.py

if [ $? -ne 0 ]; then
    echo "错误: 配置验证失败"
    exit 1
fi

echo "配置验证通过"

# 创建必要的目录
mkdir -p data logs

# 启动服务
echo "启动双115系统..."
python3 main.py
