events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 客户端上传大小限制
    client_max_body_size 100M;

    # 代理缓冲设置
    proxy_buffering off;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;

    # 上游服务器定义
    upstream emby_backend {
        server emby:8096;
    }

    upstream dual_115_backend {
        server dual-115-system:8097;
    }

    server {
        listen 80;
        server_name _;

        # 双115系统API接口
        location /api/ {
            proxy_pass http://dual_115_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            proxy_pass http://dual_115_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Emby播放请求拦截（通过双115系统处理）
        location ~* ^/emby/Videos/\d+/(stream|master\.m3u8|hls) {
            proxy_pass http://dual_115_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 传递所有原始请求头
            proxy_pass_request_headers on;

            # 禁用缓冲以支持流媒体
            proxy_buffering off;
            proxy_cache off;
        }

        # Emby字幕请求拦截
        location ~* ^/emby/Videos/\d+/Subtitles/\d+ {
            proxy_pass http://dual_115_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 传递所有原始请求头
            proxy_pass_request_headers on;

            # 字幕文件缓存
            proxy_cache_valid 200 1h;
        }

        # 其他Emby请求直接代理
        location /emby/ {
            proxy_pass http://emby_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # 传递所有原始请求头
            proxy_pass_request_headers on;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 根路径重定向到Emby
        location = / {
            return 301 /emby/;
        }

        # 静态文件直接代理到Emby
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://emby_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 静态文件缓存
            expires 1d;
            add_header Cache-Control "public, immutable";
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
