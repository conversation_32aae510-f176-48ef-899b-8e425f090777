#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双115系统 - 主115存储，副115播放
功能：
1. 遍历主115指定目录并写入数据库
2. nginx反代emby劫持播放请求
3. 从数据库获取对应文件复制到副115
4. 302播放副115文件
"""

import os
import re
import sqlite3
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from urllib.parse import unquote
import asyncio
from datetime import datetime, timedelta

from p115client import P115Client
from p115client.tool.iterdir import iter_files_with_path


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dual_115_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class Dual115System:
    """双115系统管理类"""
    
    def __init__(self, config: Dict):
        """
        初始化双115系统
        
        Args:
            config: 配置字典，包含：
                - main_115_cookies: 主115的cookies文件路径
                - sub_115_cookies: 副115的cookies文件路径  
                - db_path: 数据库文件路径
                - main_scan_dirs: 主115扫描目录列表
                - sub_cache_dir: 副115缓存目录ID
                - update_interval: 更新间隔(秒)
        """
        self.config = config
        self.db_path = config['db_path']
        
        # 初始化115客户端
        self.main_client = P115Client(
            Path(config['main_115_cookies']).expanduser(), 
            check_for_relogin=True
        )
        self.sub_client = P115Client(
            Path(config['sub_115_cookies']).expanduser(), 
            check_for_relogin=True
        )
        
        # 初始化数据库
        self._init_database()
        
        logger.info("双115系统初始化完成")
    
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS media_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_id TEXT UNIQUE NOT NULL,
                    file_name TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER,
                    pickcode TEXT,
                    sha1 TEXT,
                    parent_id TEXT,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_copied INTEGER DEFAULT 0,
                    sub_file_id TEXT,
                    sub_pickcode TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sync_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    scan_dir TEXT NOT NULL,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    files_count INTEGER,
                    status TEXT
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_file_path ON media_files(file_path);
                CREATE INDEX IF NOT EXISTS idx_file_name ON media_files(file_name);
                CREATE INDEX IF NOT EXISTS idx_is_copied ON media_files(is_copied);
            """)
            
        logger.info("数据库初始化完成")

    def _is_media_file(self, filename: str) -> bool:
        """判断是否是媒体文件"""
        from config import get_config
        config = get_config()
        ext = os.path.splitext(filename.lower())[1]
        return ext in config['media_extensions']

    def _upsert_file_info(self, file_info: Dict):
        """插入或更新文件信息到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO media_files
                (file_id, file_name, file_path, file_size, pickcode, sha1, parent_id, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                str(file_info.get('id', '')),
                file_info.get('name', ''),
                file_info.get('path', ''),
                file_info.get('size', 0),
                file_info.get('pickcode', ''),
                file_info.get('sha1', ''),
                str(file_info.get('parent_id', ''))
            ))
    
    def scan_main_115_directory(self, dir_id: str) -> int:
        """
        扫描主115指定目录，将文件信息写入数据库

        Args:
            dir_id: 目录ID或pickcode

        Returns:
            扫描到的文件数量
        """
        logger.info(f"开始扫描主115目录: {dir_id}")

        start_time = datetime.now()
        files_count = 0

        try:
            # 使用iter_files_with_path遍历文件
            for file_info in iter_files_with_path(
                self.main_client,
                dir_id,
                with_ancestors=True,
                escape=True
            ):
                # 只处理文件，跳过目录
                if file_info.get('is_directory'):
                    continue

                # 检查是否是媒体文件
                file_name = file_info.get('name', '')
                if not self._is_media_file(file_name):
                    continue

                # 插入或更新数据库
                self._upsert_file_info(file_info)
                files_count += 1

                if files_count % 100 == 0:
                    logger.info(f"已处理 {files_count} 个文件")

            # 记录同步日志
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO sync_log (scan_dir, start_time, end_time, files_count, status)
                    VALUES (?, ?, ?, ?, ?)
                """, (dir_id, start_time, datetime.now(), files_count, 'success'))

            logger.info(f"目录 {dir_id} 扫描完成，共 {files_count} 个文件")
            return files_count

        except Exception as e:
            logger.error(f"扫描目录 {dir_id} 失败: {e}")

            # 记录失败日志
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO sync_log (scan_dir, start_time, end_time, files_count, status)
                    VALUES (?, ?, ?, ?, ?)
                """, (dir_id, start_time, datetime.now(), 0, f'error: {str(e)}'))

            return 0
    
    def periodic_scan(self):
        """定期扫描主115目录"""
        logger.info("开始定期扫描任务")
        
        while True:
            try:
                for dir_id in self.config['main_scan_dirs']:
                    self.scan_main_115_directory(dir_id)
                
                logger.info(f"等待 {self.config['update_interval']} 秒后进行下次扫描")
                time.sleep(self.config['update_interval'])
                
            except KeyboardInterrupt:
                logger.info("收到中断信号，停止定期扫描")
                break
            except Exception as e:
                logger.error(f"定期扫描出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
    
    def parse_strm_path(self, strm_content: str) -> Optional[str]:
        """
        解析STRM文件内容，提取文件路径
        
        Args:
            strm_content: STRM文件内容，如 /CloudNAS/CloudDrive/115/云影/movies/...
            
        Returns:
            解析出的文件路径
        """
        # 移除URL编码
        decoded_path = unquote(strm_content.strip())
        
        # 提取115路径部分（去掉/CloudNAS/CloudDrive/115前缀）
        pattern = r'/CloudNAS/CloudDrive/115/(.+)'
        match = re.match(pattern, decoded_path)
        
        if match:
            return match.group(1)
        
        logger.warning(f"无法解析STRM路径: {strm_content}")
        return None
    
    def find_file_in_db(self, file_path: str) -> Optional[Dict]:
        """
        在数据库中查找文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典或None
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM media_files 
                WHERE file_path LIKE ? OR file_name = ?
                ORDER BY updated_time DESC
                LIMIT 1
            """, (f'%{file_path}%', os.path.basename(file_path)))
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def copy_file_to_sub_115(self, file_info: Dict) -> Optional[str]:
        """
        将文件从主115复制到副115
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            副115中的文件ID或None
        """
        try:
            # 复制文件到副115的缓存目录
            resp = self.sub_client.fs_copy(
                file_info['file_id'],
                pid=self.config['sub_cache_dir']
            )
            
            if resp.get('state'):
                sub_file_id = resp.get('data', {}).get('file_id')
                
                # 更新数据库记录
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        UPDATE media_files 
                        SET is_copied = 1, sub_file_id = ?, updated_time = CURRENT_TIMESTAMP
                        WHERE file_id = ?
                    """, (sub_file_id, file_info['file_id']))
                
                logger.info(f"文件复制成功: {file_info['file_name']} -> {sub_file_id}")
                return sub_file_id
            else:
                logger.error(f"文件复制失败: {resp}")
                return None
                
        except Exception as e:
            logger.error(f"复制文件到副115失败: {e}")
            return None
    
    def get_download_url(self, file_id: str, use_sub_115: bool = False) -> Optional[str]:
        """
        获取文件下载链接
        
        Args:
            file_id: 文件ID
            use_sub_115: 是否使用副115
            
        Returns:
            下载链接或None
        """
        try:
            client = self.sub_client if use_sub_115 else self.main_client
            url = client.download_url(file_id)
            return str(url)
        except Exception as e:
            logger.error(f"获取下载链接失败: {e}")
            return None
    
    def handle_play_request(self, strm_content: str) -> Optional[str]:
        """
        处理播放请求
        
        Args:
            strm_content: STRM文件内容
            
        Returns:
            302重定向URL或None
        """
        # 解析文件路径
        file_path = self.parse_strm_path(strm_content)
        if not file_path:
            return None
        
        # 在数据库中查找文件
        file_info = self.find_file_in_db(file_path)
        if not file_info:
            logger.warning(f"数据库中未找到文件: {file_path}")
            return None
        
        # 检查是否已复制到副115
        if file_info['is_copied'] and file_info['sub_file_id']:
            # 使用副115的文件
            return self.get_download_url(file_info['sub_file_id'], use_sub_115=True)
        else:
            # 复制到副115
            sub_file_id = self.copy_file_to_sub_115(file_info)
            if sub_file_id:
                return self.get_download_url(sub_file_id, use_sub_115=True)
            else:
                # 复制失败，使用主115
                logger.warning(f"复制失败，使用主115播放: {file_path}")
                return self.get_download_url(file_info['file_id'], use_sub_115=False)





if __name__ == '__main__':
    from config import get_config
    config = get_config()
    system = Dual115System(config)

    # 启动定期扫描
    system.periodic_scan()
