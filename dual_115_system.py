# 双115系统部署指南

## 快速开始

### 1. 环境准备

- Python 3.8+
- 两个115账号（主115用于存储，副115用于播放）
- Emby服务器（已配置STRM文件）

### 2. 获取115 Cookies

#### 方法一：浏览器导出
1. 登录115网页版
2. 按F12打开开发者工具
3. 在Console中执行：`document.cookie`
4. 复制输出内容到文件

#### 方法二：使用p115client工具
```bash
pip install p115client
python -c "from p115client import P115Client; P115Client.login_scan_cookie('cookies.txt')"
```

### 3. 配置系统

复制环境变量配置文件：
```bash
cp .env.example .env
```

编辑`.env`文件，设置以下关键参数：
```bash
# 115账号配置
MAIN_115_COOKIES=/path/to/main-115-cookies.txt
SUB_115_COOKIES=/path/to/sub-115-cookies.txt

# 扫描目录配置
MAIN_SCAN_DIRS=0,123456789  # 主115目录ID
SUB_CACHE_DIR=987654321     # 副115缓存目录ID

# Emby服务器
EMBY_SERVER=http://localhost:8096
```

### 4. 获取115目录ID

1. 登录115网页版
2. 进入要扫描的目录
3. 查看浏览器地址栏URL
4. 提取`cid=`后面的数字，如：`https://115.com/?cid=123456789`

### 5. 部署方式

#### 方式一：Docker部署（推荐）

```bash
# 创建目录
mkdir -p cookies data logs

# 复制cookies文件
cp main-115-cookies.txt cookies/
cp sub-115-cookies.txt cookies/

# 修改docker-compose.yml配置
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

#### 方式二：直接运行

```bash
# 安装依赖
pip install -r requirements.txt

# 测试配置
python test_system.py

# 启动服务
python main.py
```

#### 方式三：使用启动脚本

Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

Windows:
```cmd
start.bat
```

### 6. 验证部署

1. **检查服务状态**
   ```bash
curl http://localhost:8097/health
```

2. **查看统计信息**
   ```bash
curl http://localhost:8097/api/stats
```

3. **手动触发扫描**
   ```bash
curl http://localhost:8097/api/scan
```

4. **查看字幕文件统计**
   ```bash
curl http://localhost:8097/api/stats
   # 响应包含字幕文件统计信息
```

5. **测试字幕API**
   ```bash
# 获取媒体文件的字幕列表
   curl http://localhost:8097/api/subtitles/{media_file_id}

   # 获取字幕文件下载链接
   curl http://localhost:8097/api/subtitle/{subtitle_file_id}
```

### 7. Nginx配置

如果使用独立的Nginx，添加以下配置：

```nginx
upstream dual_115_backend {
    server localhost:8097;
}

upstream emby_backend {
    server localhost:8096;
}

server {
    listen 80;
    
    # 播放请求拦截
    location ~* ^/emby/Videos/\d+/(stream|master\.m3u8|hls) {
        proxy_pass http://dual_115_backend;
        proxy_set_header Host $host;
        proxy_buffering off;
    }
    
    # 其他请求代理到Emby
    location /emby/ {
        proxy_pass http://emby_backend;
        proxy_set_header Host $host;
    }
    
    # API接口
    location /api/ {
        proxy_pass http://dual_115_backend;
        proxy_set_header Host $host;
    }
}
```

## 常见问题

### Q: cookies过期怎么办？
A: 重新获取cookies文件，替换原文件后重启服务。

### Q: 文件复制失败？
A: 检查副115存储空间，确认缓存目录ID正确。

### Q: 播放没有走115？
A: 检查STRM文件路径格式，确认nginx配置正确。

### Q: 数据库文件过大？
A: 定期清理旧数据，或使用外部数据库。

### Q: 扫描速度慢？
A: 调整扫描间隔，减少扫描目录数量。

## 监控和维护

### 日志查看
```bash
# Docker
docker-compose logs -f dual-115-system

# 直接运行
tail -f logs/dual_115_system.log
```

### 数据库维护
```bash
# 查看数据库大小
ls -lh data/dual_115_system.db

# 清理旧数据（可选）
sqlite3 data/dual_115_system.db "DELETE FROM media_files WHERE updated_time < datetime('now', '-30 days');"
```

### 性能优化
1. 调整扫描间隔（UPDATE_INTERVAL）
2. 限制扫描目录范围
3. 使用SSD存储数据库
4. 增加副115存储空间

## 安全建议

1. 定期更新cookies文件
2. 限制API接口访问
3. 使用HTTPS（生产环境）
4. 备份数据库文件
5. 监控系统资源使用

## 字幕文件处理

### 支持的字幕格式
- `.srt` - SubRip字幕（推荐）
- `.ass` - Advanced SubStation Alpha
- `.ssa` - SubStation Alpha
- `.vtt` - WebVTT字幕
- `.sub` - MicroDVD字幕

### 字幕文件命名规范
```
电影名称.语言.类型.扩展名
```

示例：
- `电影名称.srt` - 默认字幕
- `电影名称.zh.srt` - 中文字幕
- `电影名称.en.srt` - 英文字幕
- `电影名称.zh.forced.srt` - 中文强制字幕

### 字幕API接口
```bash
# 获取媒体文件的字幕列表
curl http://localhost:8097/api/subtitles/{media_file_id}

# 获取字幕文件下载链接
curl http://localhost:8097/api/subtitle/{subtitle_file_id}
```

### 字幕处理流程
1. 扫描时自动识别字幕文件
2. 建立与媒体文件的关联关系
3. 播放时自动复制相关字幕文件
4. Emby自动加载字幕文件

详细的字幕处理说明请参考 `SUBTITLE_HANDLING.md` 文档。

            'language': language,
            'subtitle_type': subtitle_type
        }

    def _upsert_file_info(self, file_info: Dict):
        """插入或更新文件信息到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO media_files
                (file_id, file_name, file_path, file_size, pickcode, sha1, parent_id, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                str(file_info.get('id', '')),
                file_info.get('name', ''),
                file_info.get('path', ''),
                file_info.get('size', 0),
                file_info.get('pickcode', ''),
                file_info.get('sha1', ''),
                str(file_info.get('parent_id', ''))
            ))

    def _process_subtitle_files(self, subtitle_files: List[Dict], media_files: Dict) -> int:
        """处理字幕文件，关联到对应的媒体文件"""
        subtitle_count = 0

        for subtitle_info in subtitle_files:
            subtitle_name = subtitle_info.get('name', '')
            subtitle_path = subtitle_info.get('path', '')

            # 查找对应的媒体文件
            media_file_id = self._find_related_media_file(subtitle_name, subtitle_path, media_files)

            if media_file_id:
                # 提取字幕语言和类型信息
                media_name = self._get_media_filename_by_id(media_file_id)
                subtitle_meta = self._extract_subtitle_info(subtitle_name, media_name)

                # 插入字幕文件信息
                self._upsert_subtitle_info(subtitle_info, media_file_id, subtitle_meta)
                subtitle_count += 1
            else:
                logger.warning(f"未找到字幕文件对应的媒体文件: {subtitle_name}")

        return subtitle_count

    def _find_related_media_file(self, subtitle_name: str, subtitle_path: str, media_files: Dict) -> Optional[str]:
        """查找字幕文件对应的媒体文件"""
        subtitle_base = os.path.splitext(subtitle_name)[0]
        subtitle_dir = os.path.dirname(subtitle_path)

        # 方法1: 精确匹配文件名（去除扩展名）
        for media_path, media_info in media_files.items():
            media_name = media_info.get('name', '')
            media_base = os.path.splitext(media_name)[0]
            media_dir = os.path.dirname(media_path)

            # 同目录下的精确匹配
            if media_dir == subtitle_dir and subtitle_base.startswith(media_base):
                return str(media_info.get('id', ''))

        # 方法2: 模糊匹配（去除常见字幕标识后匹配）
        # 移除常见的字幕标识
        clean_subtitle_base = re.sub(r'\.(zh|chs|cht|en|eng|jp|jpn|kr|kor|forced|sdh|cc).*$', '', subtitle_base, flags=re.IGNORECASE)

        for media_path, media_info in media_files.items():
            media_name = media_info.get('name', '')
            media_base = os.path.splitext(media_name)[0]
            media_dir = os.path.dirname(media_path)

            if media_dir == subtitle_dir and clean_subtitle_base == media_base:
                return str(media_info.get('id', ''))

        return None

    def _get_media_filename_by_id(self, media_file_id: str) -> str:
        """根据媒体文件ID获取文件名"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT file_name FROM media_files WHERE file_id = ?", (media_file_id,))
            row = cursor.fetchone()
            return row[0] if row else ""

    def _upsert_subtitle_info(self, subtitle_info: Dict, media_file_id: str, subtitle_meta: Dict):
        """插入或更新字幕文件信息到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO subtitle_files
                (file_id, file_name, file_path, file_size, pickcode, sha1, parent_id,
                 media_file_id, language, subtitle_type, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                str(subtitle_info.get('id', '')),
                subtitle_info.get('name', ''),
                subtitle_info.get('path', ''),
                subtitle_info.get('size', 0),
                subtitle_info.get('pickcode', ''),
                subtitle_info.get('sha1', ''),
                str(subtitle_info.get('parent_id', '')),
                media_file_id,
                subtitle_meta.get('language', '未知'),
                subtitle_meta.get('subtitle_type', '普通字幕')
            ))
    
    def scan_main_115_directory(self, dir_id: str) -> int:
        """
        扫描主115指定目录，将文件信息写入数据库

        Args:
            dir_id: 目录ID或pickcode

        Returns:
            扫描到的文件数量
        """
        logger.info(f"开始扫描主115目录: {dir_id}")

        start_time = datetime.now()
        files_count = 0

        try:
            # 使用iter_files_with_path遍历文件
            media_files = {}  # 用于关联字幕文件
            subtitle_files = []

            for file_info in iter_files_with_path(
                self.main_client,
                dir_id,
                with_ancestors=True,
                escape=True
            ):
                # 只处理文件，跳过目录
                if file_info.get('is_directory'):
                    continue

                file_name = file_info.get('name', '')
                file_path = file_info.get('path', '')

                # 处理媒体文件
                if self._is_media_file(file_name):
                    # 插入或更新数据库
                    self._upsert_file_info(file_info)
                    # 记录媒体文件信息，用于后续字幕关联
                    media_files[file_path] = file_info
                    files_count += 1

                # 处理字幕文件
                elif self._is_subtitle_file(file_name):
                    subtitle_files.append(file_info)

                if files_count % 100 == 0:
                    logger.info(f"已处理 {files_count} 个媒体文件")

            # 处理字幕文件关联
            subtitle_count = self._process_subtitle_files(subtitle_files, media_files)
            logger.info(f"处理了 {subtitle_count} 个字幕文件")

            # 记录同步日志
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO sync_log (scan_dir, start_time, end_time, files_count, status)
                    VALUES (?, ?, ?, ?, ?)
                """, (dir_id, start_time, datetime.now(), files_count, 'success'))

            logger.info(f"目录 {dir_id} 扫描完成，共 {files_count} 个文件")
            return files_count

        except Exception as e:
            logger.error(f"扫描目录 {dir_id} 失败: {e}")

            # 记录失败日志
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO sync_log (scan_dir, start_time, end_time, files_count, status)
                    VALUES (?, ?, ?, ?, ?)
                """, (dir_id, start_time, datetime.now(), 0, f'error: {str(e)}'))

            return 0
    
    def periodic_scan(self):
        """定期扫描主115目录"""
        logger.info("开始定期扫描任务")
        
        while True:
            try:
                for dir_id in self.config['main_scan_dirs']:
                    self.scan_main_115_directory(dir_id)
                
                logger.info(f"等待 {self.config['update_interval']} 秒后进行下次扫描")
                time.sleep(self.config['update_interval'])
                
            except KeyboardInterrupt:
                logger.info("收到中断信号，停止定期扫描")
                break
            except Exception as e:
                logger.error(f"定期扫描出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
    
    def parse_strm_path(self, strm_content: str) -> Optional[str]:
        """
        解析STRM文件内容，提取文件路径
        
        Args:
            strm_content: STRM文件内容，如 /CloudNAS/CloudDrive/115/云影/movies/...
            
        Returns:
            解析出的文件路径
        """
        # 移除URL编码
        decoded_path = unquote(strm_content.strip())
        
        # 提取115路径部分（去掉/CloudNAS/CloudDrive/115前缀）
        pattern = r'/CloudNAS/CloudDrive/115/(.+)'
        match = re.match(pattern, decoded_path)
        
        if match:
            return match.group(1)
        
        logger.warning(f"无法解析STRM路径: {strm_content}")
        return None
    
    def find_file_in_db(self, file_path: str) -> Optional[Dict]:
        """
        在数据库中查找文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典或None
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM media_files 
                WHERE file_path LIKE ? OR file_name = ?
                ORDER BY updated_time DESC
                LIMIT 1
            """, (f'%{file_path}%', os.path.basename(file_path)))
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def copy_file_to_sub_115(self, file_info: Dict) -> Optional[str]:
        """
        将文件从主115复制到副115

        Args:
            file_info: 文件信息字典

        Returns:
            副115中的文件ID或None
        """
        try:
            # 复制文件到副115的缓存目录
            resp = self.sub_client.fs_copy(
                file_info['file_id'],
                pid=self.config['sub_cache_dir']
            )

            if resp.get('state'):
                sub_file_id = resp.get('data', {}).get('file_id')

                # 更新数据库记录
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        UPDATE media_files
                        SET is_copied = 1, sub_file_id = ?, updated_time = CURRENT_TIMESTAMP
                        WHERE file_id = ?
                    """, (sub_file_id, file_info['file_id']))

                logger.info(f"媒体文件复制成功: {file_info['file_name']} -> {sub_file_id}")

                # 同时复制相关的字幕文件
                self._copy_related_subtitles(file_info['file_id'])

                return sub_file_id
            else:
                logger.error(f"文件复制失败: {resp}")
                return None

        except Exception as e:
            logger.error(f"复制文件到副115失败: {e}")
            return None

    def _copy_related_subtitles(self, media_file_id: str):
        """复制媒体文件相关的字幕文件"""
        try:
            # 查找相关字幕文件
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM subtitle_files
                    WHERE media_file_id = ? AND is_copied = 0
                """, (media_file_id,))

                subtitle_files = [dict(row) for row in cursor.fetchall()]

            if not subtitle_files:
                return

            logger.info(f"开始复制 {len(subtitle_files)} 个字幕文件")

            for subtitle_info in subtitle_files:
                try:
                    # 复制字幕文件到副115
                    resp = self.sub_client.fs_copy(
                        subtitle_info['file_id'],
                        pid=self.config['sub_cache_dir']
                    )

                    if resp.get('state'):
                        sub_subtitle_id = resp.get('data', {}).get('file_id')

                        # 更新字幕文件记录
                        with sqlite3.connect(self.db_path) as conn:
                            conn.execute("""
                                UPDATE subtitle_files
                                SET is_copied = 1, sub_file_id = ?, updated_time = CURRENT_TIMESTAMP
                                WHERE file_id = ?
                            """, (sub_subtitle_id, subtitle_info['file_id']))

                        logger.info(f"字幕文件复制成功: {subtitle_info['file_name']} ({subtitle_info['language']})")
                    else:
                        logger.warning(f"字幕文件复制失败: {subtitle_info['file_name']}")

                except Exception as e:
                    logger.error(f"复制字幕文件失败 {subtitle_info['file_name']}: {e}")

        except Exception as e:
            logger.error(f"复制相关字幕文件失败: {e}")
    
    def get_download_url(self, file_id: str, use_sub_115: bool = False) -> Optional[str]:
        """
        获取文件下载链接
        
        Args:
            file_id: 文件ID
            use_sub_115: 是否使用副115
            
        Returns:
            下载链接或None
        """
        try:
            client = self.sub_client if use_sub_115 else self.main_client
            url = client.download_url(file_id)
            return str(url)
        except Exception as e:
            logger.error(f"获取下载链接失败: {e}")
            return None
    
    def handle_play_request(self, strm_content: str) -> Optional[str]:
        """
        处理播放请求 - 混合架构：优先数据库，失败时实时查找

        Args:
            strm_content: STRM文件内容

        Returns:
            302重定向URL或None
        """
        # 解析文件路径
        file_path = self.parse_strm_path(strm_content)
        if not file_path:
            return None

        # 方案1: 优先从数据库查找（快速路径）
        file_info = self.find_file_in_db(file_path)

        # 方案2: 数据库未找到时，实时搜索115（兜底方案）
        if not file_info:
            logger.info(f"数据库中未找到文件，尝试实时搜索: {file_path}")
            file_info = self._search_file_realtime(file_path)

            # 如果实时找到了，更新到数据库
            if file_info:
                self._upsert_file_info(file_info)
                logger.info(f"实时搜索成功，已更新数据库: {file_info['name']}")

        if not file_info:
            logger.warning(f"文件未找到: {file_path}")
            return None

        # 检查是否已复制到副115
        if file_info['is_copied'] and file_info['sub_file_id']:
            # 验证副115文件是否仍然存在
            if self._verify_sub_file_exists(file_info['sub_file_id']):
                return self.get_download_url(file_info['sub_file_id'], use_sub_115=True)
            else:
                # 副115文件已被删除，重新复制
                logger.warning(f"副115文件已不存在，重新复制: {file_info['file_name']}")
                file_info['is_copied'] = 0
                file_info['sub_file_id'] = None

        # 复制到副115
        sub_file_id = self.copy_file_to_sub_115(file_info)
        if sub_file_id:
            return self.get_download_url(sub_file_id, use_sub_115=True)
        else:
            # 复制失败，使用主115
            logger.warning(f"复制失败，使用主115播放: {file_path}")
            return self.get_download_url(file_info['file_id'], use_sub_115=False)

    def get_subtitle_files(self, media_file_id: str) -> List[Dict]:
        """获取媒体文件的字幕文件列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM subtitle_files
                    WHERE media_file_id = ?
                    ORDER BY language, subtitle_type
                """, (media_file_id,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"获取字幕文件列表失败: {e}")
            return []

    def get_subtitle_download_urls(self, media_file_id: str, use_sub_115: bool = True) -> List[Dict]:
        """获取字幕文件的下载链接"""
        subtitle_files = self.get_subtitle_files(media_file_id)
        subtitle_urls = []

        for subtitle_info in subtitle_files:
            try:
                # 优先使用副115的字幕文件
                if use_sub_115 and subtitle_info.get('is_copied') and subtitle_info.get('sub_file_id'):
                    url = self.get_download_url(subtitle_info['sub_file_id'], use_sub_115=True)
                else:
                    url = self.get_download_url(subtitle_info['file_id'], use_sub_115=False)

                if url:
                    subtitle_urls.append({
                        'file_id': subtitle_info['file_id'],
                        'file_name': subtitle_info['file_name'],
                        'language': subtitle_info['language'],
                        'subtitle_type': subtitle_info['subtitle_type'],
                        'url': url,
                        'is_copied': subtitle_info['is_copied']
                    })

            except Exception as e:
                logger.error(f"获取字幕下载链接失败 {subtitle_info['file_name']}: {e}")

        return subtitle_urls

    def _search_file_realtime(self, file_path: str) -> Optional[Dict]:
        """实时搜索115文件（兜底方案）"""
        try:
            # 提取文件名进行搜索
            filename = os.path.basename(file_path)
            base_name = os.path.splitext(filename)[0]

            # 使用115搜索API
            search_results = self.main_client.fs_search(base_name)

            if not search_results.get('data'):
                return None

            # 查找最匹配的文件
            for item in search_results['data']:
                if item.get('n') == filename:  # 精确匹配文件名
                    return {
                        'id': str(item.get('fid', '')),
                        'name': item.get('n', ''),
                        'path': file_path,  # 使用STRM中的路径
                        'size': item.get('s', 0),
                        'pickcode': item.get('pc', ''),
                        'sha1': item.get('sha', ''),
                        'parent_id': str(item.get('pid', ''))
                    }

            return None

        except Exception as e:
            logger.error(f"实时搜索文件失败: {e}")
            return None

    def _verify_sub_file_exists(self, sub_file_id: str) -> bool:
        """验证副115中的文件是否仍然存在"""
        try:
            # 尝试获取文件信息
            resp = self.sub_client.fs_file_skim(sub_file_id)
            return resp.get('state', False)
        except Exception as e:
            logger.debug(f"验证副115文件存在性失败: {e}")
            return False





if __name__ == '__main__':
    from config import get_config
    config = get_config()
    system = Dual115System(config)

    # 启动定期扫描
    system.periodic_scan()
