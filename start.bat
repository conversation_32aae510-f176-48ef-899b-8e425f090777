@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === 双115系统启动脚本 ===

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo Python检查通过

REM 检查必要文件
set "required_files=dual_115_system.py nginx_proxy.py config.py main.py requirements.txt"
for %%f in (%required_files%) do (
    if not exist "%%f" (
        echo 错误: 缺少必要文件: %%f
        pause
        exit /b 1
    )
)

echo 文件检查通过

REM 安装依赖
echo 安装Python依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

REM 验证配置
echo 验证配置...
python config.py
if errorlevel 1 (
    echo 错误: 配置验证失败
    pause
    exit /b 1
)

echo 配置验证通过

REM 创建必要目录
if not exist "data" mkdir data
if not exist "logs" mkdir logs

REM 启动服务
echo 启动双115系统...
python main.py

pause
