#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nginx反代Emby的Flask应用
劫持播放请求，处理STRM文件播放
"""

import re
import logging
from flask import Flask, request, redirect, jsonify, Response
from urllib.parse import unquote
import requests

from dual_115_system import Dual115System
from config import get_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 初始化双115系统
config = get_config()
dual_system = Dual115System(config)

# Emby服务器配置
EMBY_SERVER = "http://localhost:8096"  # 实际的Emby服务器地址


@app.route('/emby/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
def proxy_emby(path):
    """代理Emby请求"""
    
    # 检查是否是播放请求
    if is_play_request(path, request.args):
        return handle_play_request(path, request.args)
    
    # 其他请求直接代理到Emby
    return proxy_to_emby(path)


def is_play_request(path: str, args: dict) -> bool:
    """判断是否是播放请求"""
    # 检查路径是否包含播放相关的端点
    play_patterns = [
        r'/Videos/\d+/stream',
        r'/Videos/\d+/master\.m3u8',
        r'/Videos/\d+/hls',
        r'/Audio/\d+/stream',
    ]
    
    for pattern in play_patterns:
        if re.search(pattern, path):
            return True
    
    # 检查查询参数
    if 'MediaSourceId' in args or 'PlaySessionId' in args:
        return True
    
    return False


def handle_play_request(path: str, args: dict) -> Response:
    """处理播放请求"""
    logger.info(f"处理播放请求: {path}, 参数: {args}")
    
    try:
        # 首先获取媒体项信息
        media_info = get_media_info_from_emby(path, args)
        if not media_info:
            logger.warning("无法获取媒体信息，直接代理")
            return proxy_to_emby(path)
        
        # 检查是否是STRM文件
        if not is_strm_file(media_info):
            logger.info("非STRM文件，直接代理")
            return proxy_to_emby(path)
        
        # 获取STRM文件内容
        strm_content = get_strm_content(media_info)
        if not strm_content:
            logger.warning("无法获取STRM内容，直接代理")
            return proxy_to_emby(path)
        
        # 处理115播放
        play_url = dual_system.handle_play_request(strm_content)
        if play_url:
            logger.info(f"重定向到115播放链接: {play_url}")
            return redirect(play_url, code=302)
        else:
            logger.warning("无法获取115播放链接，直接代理")
            return proxy_to_emby(path)
            
    except Exception as e:
        logger.error(f"处理播放请求失败: {e}")
        return proxy_to_emby(path)


def get_media_info_from_emby(path: str, args: dict) -> dict:
    """从Emby获取媒体信息"""
    try:
        # 从路径中提取媒体ID
        media_id_match = re.search(r'/Videos/(\d+)/', path)
        if not media_id_match:
            return None
        
        media_id = media_id_match.group(1)
        
        # 调用Emby API获取媒体信息
        api_url = f"{EMBY_SERVER}/emby/Items/{media_id}"
        
        # 转发原始请求的认证信息
        headers = {}
        if 'Authorization' in request.headers:
            headers['Authorization'] = request.headers['Authorization']
        if 'X-Emby-Token' in request.headers:
            headers['X-Emby-Token'] = request.headers['X-Emby-Token']
        
        response = requests.get(api_url, headers=headers, params=request.args)
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.warning(f"获取媒体信息失败: {response.status_code}")
            return None
            
    except Exception as e:
        logger.error(f"获取媒体信息异常: {e}")
        return None


def is_strm_file(media_info: dict) -> bool:
    """判断是否是STRM文件"""
    if not media_info:
        return False
    
    # 检查文件扩展名
    path = media_info.get('Path', '')
    if path.lower().endswith('.strm'):
        return True
    
    # 检查媒体源
    media_sources = media_info.get('MediaSources', [])
    for source in media_sources:
        if source.get('Path', '').lower().endswith('.strm'):
            return True
    
    return False


def get_strm_content(media_info: dict) -> str:
    """获取STRM文件内容"""
    try:
        # 获取文件路径
        strm_path = media_info.get('Path', '')
        if not strm_path:
            media_sources = media_info.get('MediaSources', [])
            if media_sources:
                strm_path = media_sources[0].get('Path', '')
        
        if not strm_path:
            return None
        
        # 读取STRM文件内容
        with open(strm_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        logger.info(f"STRM文件内容: {content}")
        return content
        
    except Exception as e:
        logger.error(f"读取STRM文件失败: {e}")
        return None


def proxy_to_emby(path: str) -> Response:
    """代理请求到Emby服务器"""
    try:
        url = f"{EMBY_SERVER}/emby/{path}"
        
        # 转发请求头
        headers = {}
        for key, value in request.headers:
            if key.lower() not in ['host', 'content-length']:
                headers[key] = value
        
        # 转发请求
        if request.method == 'GET':
            resp = requests.get(url, headers=headers, params=request.args, stream=True)
        elif request.method == 'POST':
            resp = requests.post(url, headers=headers, params=request.args, 
                               data=request.get_data(), stream=True)
        elif request.method == 'PUT':
            resp = requests.put(url, headers=headers, params=request.args, 
                              data=request.get_data(), stream=True)
        elif request.method == 'DELETE':
            resp = requests.delete(url, headers=headers, params=request.args)
        else:
            resp = requests.request(request.method, url, headers=headers, 
                                  params=request.args, data=request.get_data(), stream=True)
        
        # 构建响应
        def generate():
            for chunk in resp.iter_content(chunk_size=8192):
                yield chunk
        
        response = Response(generate(), status=resp.status_code)
        
        # 转发响应头
        for key, value in resp.headers.items():
            if key.lower() not in ['content-encoding', 'content-length', 'transfer-encoding']:
                response.headers[key] = value
        
        return response
        
    except Exception as e:
        logger.error(f"代理请求失败: {e}")
        return jsonify({'error': 'Proxy failed'}), 500


@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({'status': 'ok', 'service': 'nginx-proxy'})


@app.route('/api/scan')
def manual_scan():
    """手动触发扫描"""
    try:
        total_files = 0
        for dir_id in config['main_scan_dirs']:
            files_count = dual_system.scan_main_115_directory(dir_id)
            total_files += files_count
        
        return jsonify({
            'status': 'success',
            'message': f'扫描完成，共处理 {total_files} 个文件'
        })
    except Exception as e:
        logger.error(f"手动扫描失败: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@app.route('/api/stats')
def get_stats():
    """获取统计信息"""
    try:
        import sqlite3
        with sqlite3.connect(config['db_path']) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM media_files")
            total_files = cursor.fetchone()[0]

            cursor = conn.execute("SELECT COUNT(*) FROM media_files WHERE is_copied = 1")
            copied_files = cursor.fetchone()[0]

            cursor = conn.execute("SELECT COUNT(*) FROM subtitle_files")
            total_subtitles = cursor.fetchone()[0]

            cursor = conn.execute("SELECT COUNT(*) FROM subtitle_files WHERE is_copied = 1")
            copied_subtitles = cursor.fetchone()[0]

        return jsonify({
            'total_files': total_files,
            'copied_files': copied_files,
            'copy_rate': f"{copied_files/total_files*100:.1f}%" if total_files > 0 else "0%",
            'total_subtitles': total_subtitles,
            'copied_subtitles': copied_subtitles,
            'subtitle_copy_rate': f"{copied_subtitles/total_subtitles*100:.1f}%" if total_subtitles > 0 else "0%"
        })
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@app.route('/api/subtitles/<media_file_id>')
def get_subtitles(media_file_id):
    """获取指定媒体文件的字幕列表"""
    try:
        subtitle_urls = dual_system.get_subtitle_download_urls(media_file_id)
        return jsonify({
            'status': 'success',
            'media_file_id': media_file_id,
            'subtitles': subtitle_urls
        })
    except Exception as e:
        logger.error(f"获取字幕列表失败: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@app.route('/api/subtitle/<subtitle_file_id>')
def get_subtitle_url(subtitle_file_id):
    """获取字幕文件下载链接"""
    try:
        # 查找字幕文件信息
        import sqlite3
        with sqlite3.connect(config['db_path']) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM subtitle_files WHERE file_id = ?", (subtitle_file_id,))
            subtitle_info = cursor.fetchone()

        if not subtitle_info:
            return jsonify({'status': 'error', 'message': '字幕文件不存在'}), 404

        subtitle_dict = dict(subtitle_info)

        # 获取下载链接
        if subtitle_dict.get('is_copied') and subtitle_dict.get('sub_file_id'):
            url = dual_system.get_download_url(subtitle_dict['sub_file_id'], use_sub_115=True)
        else:
            url = dual_system.get_download_url(subtitle_dict['file_id'], use_sub_115=False)

        if url:
            return jsonify({
                'status': 'success',
                'subtitle_info': {
                    'file_id': subtitle_dict['file_id'],
                    'file_name': subtitle_dict['file_name'],
                    'language': subtitle_dict['language'],
                    'subtitle_type': subtitle_dict['subtitle_type'],
                    'is_copied': subtitle_dict['is_copied']
                },
                'url': url
            })
        else:
            return jsonify({'status': 'error', 'message': '无法获取下载链接'}), 500

    except Exception as e:
        logger.error(f"获取字幕下载链接失败: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


if __name__ == '__main__':
    # 启动Flask应用
    app.run(host='0.0.0.0', port=8097, debug=False)
