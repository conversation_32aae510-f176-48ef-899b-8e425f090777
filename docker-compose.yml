version: '3.8'

services:
  dual-115-system:
    build: .
    container_name: dual-115-system
    restart: unless-stopped
    ports:
      - "8097:8097"
    volumes:
      # 115 cookies文件
      - ./cookies:/app/cookies:ro
      # 数据库持久化
      - ./data:/app/data
      # 日志目录
      - ./logs:/app/logs
      # Emby媒体库目录（用于读取STRM文件）
      - /path/to/emby/media:/media:ro
    environment:
      # 115账号配置
      - MAIN_115_COOKIES=/app/cookies/main-115-cookies.txt
      - SUB_115_COOKIES=/app/cookies/sub-115-cookies.txt
      
      # 数据库配置
      - DB_PATH=/app/data/dual_115_system.db
      
      # 扫描配置
      - MAIN_SCAN_DIRS=0,123456789  # 主115扫描目录ID，多个用逗号分隔
      - SUB_CACHE_DIR=987654321     # 副115缓存目录ID
      - UPDATE_INTERVAL=3600        # 更新间隔（秒）
      
      # Emby服务器配置
      - EMBY_SERVER=http://emby:8096  # Emby服务器地址
      
      # Flask应用配置
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=8097
      - FLASK_DEBUG=false
      
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=/app/logs/dual_115_system.log
    
    depends_on:
      - emby
    
    networks:
      - dual-115-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: dual-115-nginx
    restart: unless-stopped
    ports:
      - "8096:80"  # 将8096端口映射到nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - dual-115-system
      - emby
    networks:
      - dual-115-network

  # Emby服务器（示例，如果已有可以移除）
  emby:
    image: emby/embyserver:latest
    container_name: emby-server
    restart: unless-stopped
    ports:
      - "8098:8096"  # 内部端口，通过nginx代理访问
    volumes:
      - ./emby-config:/config
      - /path/to/emby/media:/media:ro
    environment:
      - UID=1000
      - GID=1000
    networks:
      - dual-115-network

networks:
  dual-115-network:
    driver: bridge

volumes:
  emby-config:
  dual-115-data:
