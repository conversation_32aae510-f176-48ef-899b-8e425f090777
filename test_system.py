#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双115系统测试脚本
"""

import os
import sys
import sqlite3
import requests
from pathlib import Path

def test_config():
    """测试配置"""
    print("=== 测试配置 ===")
    
    try:
        from config import get_config, validate_config
        
        config = get_config()
        print(f"配置加载成功: {len(config)} 项配置")
        
        errors = validate_config()
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        else:
            print("配置验证通过")
            return True
            
    except Exception as e:
        print(f"配置测试失败: {e}")
        return False

def test_115_connection():
    """测试115连接"""
    print("\n=== 测试115连接 ===")
    
    try:
        from config import get_config
        from p115client import P115Client
        
        config = get_config()
        
        # 测试主115
        print("测试主115连接...")
        main_cookies_path = Path(config['main_115_cookies']).expanduser()
        if not main_cookies_path.exists():
            print(f"主115 cookies文件不存在: {main_cookies_path}")
            return False
            
        main_client = P115Client(main_cookies_path, check_for_relogin=True)
        user_info = main_client.user_info()
        print(f"主115连接成功: {user_info.get('user_name', 'Unknown')}")
        
        # 测试副115
        print("测试副115连接...")
        sub_cookies_path = Path(config['sub_115_cookies']).expanduser()
        if not sub_cookies_path.exists():
            print(f"副115 cookies文件不存在: {sub_cookies_path}")
            return False
            
        sub_client = P115Client(sub_cookies_path, check_for_relogin=True)
        user_info = sub_client.user_info()
        print(f"副115连接成功: {user_info.get('user_name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"115连接测试失败: {e}")
        return False

def test_database():
    """测试数据库"""
    print("\n=== 测试数据库 ===")
    
    try:
        from config import get_config
        from dual_115_system import Dual115System
        
        config = get_config()
        
        # 创建测试数据库
        test_db_path = "test_dual_115_system.db"
        test_config = config.copy()
        test_config['db_path'] = test_db_path
        
        system = Dual115System(test_config)
        
        # 测试数据库连接
        with sqlite3.connect(test_db_path) as conn:
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"数据库表创建成功: {tables}")
        
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        print("数据库测试通过")
        return True
        
    except Exception as e:
        print(f"数据库测试失败: {e}")
        return False

def test_api_server():
    """测试API服务器"""
    print("\n=== 测试API服务器 ===")
    
    try:
        from config import get_config
        
        config = get_config()
        base_url = f"http://{config['flask_host']}:{config['flask_port']}"
        
        # 测试健康检查
        print("测试健康检查接口...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("健康检查接口正常")
        else:
            print(f"健康检查接口异常: {response.status_code}")
            return False
        
        # 测试统计接口
        print("测试统计接口...")
        response = requests.get(f"{base_url}/api/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"统计接口正常: {stats}")
        else:
            print(f"统计接口异常: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("API服务器未启动，跳过测试")
        return True
    except Exception as e:
        print(f"API服务器测试失败: {e}")
        return False

def test_strm_parsing():
    """测试STRM路径解析"""
    print("\n=== 测试STRM路径解析 ===")
    
    try:
        from dual_115_system import Dual115System
        from config import get_config
        
        config = get_config()
        system = Dual115System(config)
        
        # 测试用例
        test_cases = [
            "/CloudNAS/CloudDrive/115/云影/movies/外语电影/2023/极拳攻缉 (2023) {tmdbid-959092}/极拳攻缉 (2023) {tmdbid-959092} - 2160p.Ultra HD BluRay.Remux Dolby Vision HDR 10-bit.H.265.DTS-HD MA 5.1-老K.mkv",
            "/CloudNAS/CloudDrive/115/电影/动作片/test.mp4",
            "invalid_path",
        ]
        
        for test_case in test_cases:
            result = system.parse_strm_path(test_case)
            print(f"输入: {test_case}")
            print(f"输出: {result}")
            print()
        
        print("STRM路径解析测试完成")
        return True
        
    except Exception as e:
        print(f"STRM路径解析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("双115系统测试开始")
    print("=" * 50)
    
    tests = [
        ("配置测试", test_config),
        ("115连接测试", test_115_connection),
        ("数据库测试", test_database),
        ("API服务器测试", test_api_server),
        ("STRM路径解析测试", test_strm_parsing),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("所有测试通过！系统可以正常运行。")
        return 0
    else:
        print("部分测试失败，请检查配置和环境。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
