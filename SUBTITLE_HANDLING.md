# 双115系统外挂字幕处理说明

## 概述

双115系统完整支持外挂字幕文件的处理，包括自动识别、关联、复制和播放。系统会在复制媒体文件时自动处理相关的字幕文件。

## 支持的字幕格式

### 115官方支持的字幕格式
- `.srt` - SubRip字幕（推荐）
- `.ass` - Advanced SubStation Alpha
- `.ssa` - SubStation Alpha

### 系统识别的字幕格式
- `.vtt` - WebVTT字幕
- `.sub` - MicroDVD字幕
- `.idx` - VobSub索引文件

## 字幕文件命名规范

### 标准命名格式
```
电影名称.语言.字幕类型.扩展名
```

### 示例
```
极拳攻缉 (2023).srt                    # 默认字幕
极拳攻缉 (2023).zh.srt                 # 中文字幕
极拳攻缉 (2023).en.srt                 # 英文字幕
极拳攻缉 (2023).zh.forced.srt          # 中文强制字幕
极拳攻缉 (2023).en.sdh.srt             # 英文SDH字幕
```

### 支持的语言标识
- `zh`, `chs`, `cn` - 中文/简体中文
- `cht` - 繁体中文
- `en`, `eng`, `english` - 英文
- `jp`, `jpn`, `japanese` - 日文
- `kr`, `kor`, `korean` - 韩文
- `fr`, `french` - 法文
- `de`, `german` - 德文
- `es`, `spanish` - 西班牙文

### 支持的字幕类型标识
- `forced` - 强制字幕
- `sdh` - SDH字幕（听障辅助）
- `cc` - CC字幕（隐藏字幕）
- `full` - 完整字幕

## 字幕处理流程

### 1. 扫描阶段
```
主115目录扫描 → 识别媒体文件 → 识别字幕文件 → 建立关联关系 → 存入数据库
```

### 2. 关联算法
1. **精确匹配**：同目录下文件名前缀完全匹配
2. **模糊匹配**：移除语言和类型标识后匹配
3. **目录匹配**：确保字幕和媒体文件在同一目录

### 3. 播放阶段
```
播放请求 → 复制媒体文件 → 自动复制相关字幕 → 返回播放链接
```

## 数据库结构

### 字幕文件表 (subtitle_files)
```sql
CREATE TABLE subtitle_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_id TEXT UNIQUE NOT NULL,           -- 115文件ID
    file_name TEXT NOT NULL,                -- 文件名
    file_path TEXT NOT NULL,                -- 文件路径
    file_size INTEGER,                      -- 文件大小
    pickcode TEXT,                          -- 115 pickcode
    sha1 TEXT,                              -- 文件SHA1
    parent_id TEXT,                         -- 父目录ID
    media_file_id TEXT,                     -- 关联的媒体文件ID
    language TEXT,                          -- 字幕语言
    subtitle_type TEXT,                     -- 字幕类型
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_copied INTEGER DEFAULT 0,           -- 是否已复制到副115
    sub_file_id TEXT,                       -- 副115中的文件ID
    sub_pickcode TEXT,                      -- 副115中的pickcode
    FOREIGN KEY (media_file_id) REFERENCES media_files (file_id)
);
```

## API接口

### 1. 获取媒体文件的字幕列表
```http
GET /api/subtitles/{media_file_id}
```

**响应示例：**
```json
{
    "status": "success",
    "media_file_id": "123456789",
    "subtitles": [
        {
            "file_id": "987654321",
            "file_name": "电影名称.zh.srt",
            "language": "中文",
            "subtitle_type": "普通字幕",
            "url": "https://115.com/download/...",
            "is_copied": true
        }
    ]
}
```

### 2. 获取字幕文件下载链接
```http
GET /api/subtitle/{subtitle_file_id}
```

**响应示例：**
```json
{
    "status": "success",
    "subtitle_info": {
        "file_id": "987654321",
        "file_name": "电影名称.zh.srt",
        "language": "中文",
        "subtitle_type": "普通字幕",
        "is_copied": true
    },
    "url": "https://115.com/download/..."
}
```

### 3. 统计信息（包含字幕）
```http
GET /api/stats
```

**响应示例：**
```json
{
    "total_files": 1000,
    "copied_files": 800,
    "copy_rate": "80.0%",
    "total_subtitles": 1500,
    "copied_subtitles": 1200,
    "subtitle_copy_rate": "80.0%"
}
```

## Emby集成

### 字幕请求拦截
系统会拦截Emby的字幕请求：
```
/emby/Videos/{id}/Subtitles/{index}
```

### 自动字幕加载
- Emby会自动发现同目录下的字幕文件
- 系统确保字幕文件与媒体文件同步复制
- 支持多语言字幕切换

## 配置选项

### 环境变量
```bash
# 字幕文件处理开关（默认开启）
ENABLE_SUBTITLE_PROCESSING=true

# 字幕复制延迟（秒，避免并发问题）
SUBTITLE_COPY_DELAY=1

# 字幕文件缓存时间（秒）
SUBTITLE_CACHE_TIME=3600
```

## 故障排除

### 常见问题

1. **字幕文件未关联到媒体文件**
   - 检查文件命名是否符合规范
   - 确保字幕和媒体文件在同一目录
   - 查看日志中的关联失败信息

2. **字幕文件未复制**
   - 检查副115存储空间
   - 确认字幕文件格式被115支持
   - 查看复制日志

3. **Emby中字幕不显示**
   - 确认字幕文件已复制到副115
   - 检查Emby字幕设置
   - 验证字幕文件编码格式

### 调试命令

```bash
# 查看字幕文件统计
curl http://localhost:8097/api/stats

# 查看特定媒体文件的字幕
curl http://localhost:8097/api/subtitles/123456789

# 查看字幕文件下载链接
curl http://localhost:8097/api/subtitle/987654321
```

### 日志关键词
- `字幕文件复制成功`
- `未找到字幕文件对应的媒体文件`
- `字幕文件关联成功`
- `处理了 X 个字幕文件`

## 最佳实践

### 1. 文件组织
```
电影目录/
├── 电影名称.mkv
├── 电影名称.zh.srt
├── 电影名称.en.srt
└── 电影名称.zh.forced.srt
```

### 2. 命名建议
- 使用标准的语言代码
- 明确标识字幕类型
- 保持文件名一致性

### 3. 性能优化
- 定期清理未关联的字幕文件
- 监控副115存储空间
- 合理设置扫描间隔

## 技术实现

### 字幕识别算法
1. 扫描目录时同时收集媒体文件和字幕文件
2. 使用文件名匹配算法建立关联
3. 提取语言和类型信息存储到数据库

### 复制策略
1. 媒体文件复制成功后触发字幕复制
2. 批量复制相关字幕文件
3. 更新数据库状态

### 缓存机制
1. 字幕文件URL缓存1小时
2. 数据库查询结果缓存
3. 失败重试机制

这个字幕处理系统确保了外挂字幕文件能够与媒体文件同步管理，为用户提供完整的观影体验。
