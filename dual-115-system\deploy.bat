@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === 双115系统Docker部署脚本 ===
echo.

REM 检查Docker
echo [INFO] 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] Docker环境检查通过
echo.

REM 创建目录
echo [INFO] 创建必要的目录...
if not exist "cookies" mkdir cookies
if not exist "data" mkdir data
if not exist "logs" mkdir logs
echo [SUCCESS] 目录创建完成
echo.

REM 检查cookies文件
echo [INFO] 检查115 cookies文件...
if not exist "cookies\main-115-cookies.txt" (
    echo [WARNING] 主115 cookies文件不存在: cookies\main-115-cookies.txt
    echo [INFO] 请将主115的cookies文件复制到 cookies\main-115-cookies.txt
    set /p continue="是否继续部署？(y/N): "
    if /i not "!continue!"=="y" exit /b 1
)

if not exist "cookies\sub-115-cookies.txt" (
    echo [WARNING] 副115 cookies文件不存在: cookies\sub-115-cookies.txt
    echo [INFO] 请将副115的cookies文件复制到 cookies\sub-115-cookies.txt
    set /p continue="是否继续部署？(y/N): "
    if /i not "!continue!"=="y" exit /b 1
)

echo [SUCCESS] Cookies文件检查完成
echo.

REM 配置向导
echo [INFO] 开始配置向导...
if not exist ".env" (
    echo [INFO] 创建.env配置文件...
    copy .env.example .env >nul
    
    echo [INFO] 请编辑.env文件配置以下参数：
    echo [INFO] 1. MAIN_SCAN_DIRS - 主115扫描目录ID
    echo [INFO] 2. SUB_CACHE_DIR - 副115缓存目录ID
    echo [INFO] 3. EMBY_SERVER - Emby服务器地址
    echo [INFO] 4. 其他可选参数
    
    set /p edit="是否现在编辑配置文件？(Y/n): "
    if /i not "!edit!"=="n" notepad .env
) else (
    echo [INFO] 发现现有.env配置文件
)

echo [SUCCESS] 配置完成
echo.

REM 构建和启动服务
echo [INFO] 构建Docker镜像...
docker-compose build
if errorlevel 1 (
    echo [ERROR] Docker镜像构建失败
    pause
    exit /b 1
)

echo [INFO] 启动服务...
docker-compose up -d
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    pause
    exit /b 1
)

echo [SUCCESS] 服务启动完成
echo.

REM 验证部署
echo [INFO] 验证部署状态...
timeout /t 10 /nobreak >nul

REM 检查容器状态
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] 容器启动失败
    docker-compose logs
    pause
    exit /b 1
)

echo [SUCCESS] 容器启动成功

REM 检查健康状态
echo [INFO] 检查服务健康状态...
for /l %%i in (1,1,30) do (
    curl -s http://localhost:8097/health >nul 2>&1
    if not errorlevel 1 (
        echo [SUCCESS] 服务健康检查通过
        goto :health_ok
    )
    timeout /t 2 /nobreak >nul
)

echo [ERROR] 服务健康检查失败
docker-compose logs dual-115-system
pause
exit /b 1

:health_ok

REM 显示部署信息
echo.
echo [SUCCESS] === 部署完成 ===
echo.
echo [INFO] 服务访问地址：
echo [INFO]   - 双115系统API: http://localhost:8097
echo [INFO]   - Emby代理地址: http://localhost:8096
echo [INFO]   - 健康检查: http://localhost:8097/health
echo.
echo [INFO] 常用命令：
echo [INFO]   - 查看日志: docker-compose logs -f dual-115-system
echo [INFO]   - 重启服务: docker-compose restart
echo [INFO]   - 停止服务: docker-compose down
echo [INFO]   - 手动扫描: curl http://localhost:8097/api/scan
echo [INFO]   - 查看统计: curl http://localhost:8097/api/stats
echo.
echo [INFO] 配置文件位置：
echo [INFO]   - 环境变量: .env
echo [INFO]   - Docker编排: docker-compose.yml
echo [INFO]   - Nginx配置: nginx.conf
echo.
echo [WARNING] 注意事项：
echo [WARNING]   - 请确保115 cookies文件有效
echo [WARNING]   - 定期检查副115存储空间
echo [WARNING]   - 监控系统日志和性能
echo.
echo [SUCCESS] 双115系统部署完成！

pause
